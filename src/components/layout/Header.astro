---
import LanguageSwitcher from './LanguageSwitcher.astro';
import { getLanguageFromURL, t } from '../../i18n/index.js';

// 获取当前语言
let currentLang = getLanguageFromURL(Astro.request.url);

// 检查 URL 参数中的语言设置
const url = new URL(Astro.request.url);
const langParam = url.searchParams.get('lang');
if (langParam && ['en', 'ar', 'pt'].includes(langParam)) {
  currentLang = langParam;
}
---

<header class="w-full bg-black bg-opacity-95 backdrop-blur-sm sticky top-0 z-10 shadow-md">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">

    <!-- Desktop Layout: Left-Right Layout -->
    <div class="hidden sm:flex sm:items-center sm:justify-between sm:py-4">
      <!-- Left: Desktop Search Box -->
      <div class="flex-1 max-w-2xl">
        <div class="relative">
          <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
            <svg class="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd" />
            </svg>
          </div>
          <input
            type="text"
            id="globalSearch"
            placeholder="Search songs, artists, albums..."
            class="block w-full pl-12 pr-4 py-3 bg-gray-700/80 backdrop-blur-sm border-2 border-gray-500/60 rounded-xl text-white placeholder-gray-300 focus:outline-none focus:bg-gray-600/90 focus:border-primary/80 focus:ring-2 focus:ring-primary/30 hover:border-gray-400/70 transition-all duration-200 text-sm shadow-xl"
            autocomplete="off"
          />
          <!-- Search Results Dropdown -->
          <div id="searchResults" class="absolute top-full left-0 right-0 mt-2 bg-gray-800/95 backdrop-blur-md border border-gray-600/50 rounded-xl shadow-2xl max-h-96 overflow-y-auto z-50 hidden">
            <!-- Search results will be displayed here -->
          </div>
        </div>
      </div>

      <!-- Right: Desktop Language Switcher -->
      <div class="ml-6 flex-shrink-0">
        <LanguageSwitcher currentLocale={currentLang} />
      </div>
    </div>

    <!-- Mobile Layout: Two Rows -->
    <div class="sm:hidden">
      <!-- First Row: Logo + Language Switcher -->
      <div class="flex justify-between items-center py-4">
        <!-- Mobile Logo -->
        <div class="flex items-center">
          <a href={currentLang === 'en' ? '/' : `/${currentLang}/`} class="flex items-center">
            <div class="w-8 h-8 bg-primary rounded-full flex items-center justify-center">
              <svg class="w-5 h-5 text-black" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z" stroke="currentColor" stroke-width="2"/>
                <path d="M6.85046 16C7.17627 16.6667 8.82373 16.6667 9.14954 16L12 10.9282L14.8505 16C15.1763 16.6667 16.8237 16.6667 17.1495 16L20 10.9282" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                <path d="M9 9C9 9.55228 8.55228 10 8 10C7.44772 10 7 9.55228 7 9C7 8.44772 7.44772 8 8 8C8.55228 8 9 8.44772 9 9Z" fill="currentColor"/>
                <path d="M17 9C17 9.55228 16.5523 10 16 10C15.4477 10 15 9.55228 15 9C15 8.44772 15.4477 8 16 8C16.5523 8 17 8.44772 17 9Z" fill="currentColor"/>
              </svg>
            </div>
            <span class="ml-2 text-lg font-medium">AITrackerHive</span>
          </a>
        </div>

        <!-- Mobile Language Switcher -->
        <div>
          <LanguageSwitcher currentLocale={currentLang} />
        </div>
      </div>

      <!-- Second Row: Full Width Search Box -->
      <div class="pb-4">
        <div class="relative">
          <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
            <svg class="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd" />
            </svg>
          </div>
          <input
            type="text"
            id="mobileGlobalSearch"
            placeholder="Search songs, artists, albums..."
            class="block w-full pl-12 pr-4 py-3 bg-gray-700/80 backdrop-blur-sm border-2 border-gray-500/60 rounded-xl text-white placeholder-gray-300 focus:outline-none focus:bg-gray-600/90 focus:border-primary/80 focus:ring-2 focus:ring-primary/30 hover:border-gray-400/70 transition-all duration-200 text-base shadow-xl"
            autocomplete="off"
          />
          <!-- Mobile Search Results Dropdown -->
          <div id="mobileSearchResults" class="absolute top-full left-0 right-0 mt-2 bg-gray-800/95 backdrop-blur-md border border-gray-600/50 rounded-xl shadow-xl max-h-80 overflow-y-auto z-50 hidden">
            <!-- Search results will be displayed here -->
          </div>
        </div>
      </div>
    </div>

  </div>
</header>

<script>
  // 全局搜索功能
  function initializeSearch() {
    const desktopSearchInput = document.getElementById('globalSearch');
    const mobileSearchInput = document.getElementById('mobileGlobalSearch');
    const desktopSearchResults = document.getElementById('searchResults');
    const mobileSearchResults = document.getElementById('mobileSearchResults');

    // Check if already initialized to avoid duplicate event binding
    if (desktopSearchInput && desktopSearchInput.hasAttribute('data-search-initialized')) {
      return;
    }

    let searchTimeout;
    let currentSearchTerm = '';

    // Mark as initialized
    if (desktopSearchInput) {
      desktopSearchInput.setAttribute('data-search-initialized', 'true');
    }
    if (mobileSearchInput) {
      mobileSearchInput.setAttribute('data-search-initialized', 'true');
    }

    // Fill search boxes with query parameter from URL
    const urlParams = new URLSearchParams(window.location.search);
    const queryParam = urlParams.get('q');
    if (queryParam) {
      if (desktopSearchInput) {
        desktopSearchInput.value = queryParam;
      }
      if (mobileSearchInput) {
        mobileSearchInput.value = queryParam;
      }
    }

    // Get current language and search path (English only for now)
    function getCurrentLanguageAndSearchPath() {
      return {
        language: 'en',
        searchPath: '/search',
        apiPath: '/api/search.json'
      };
    }

    // Simple search function - show basic suggestions only
    async function performSearch(query, resultsContainer) {
      if (!query.trim()) {
        resultsContainer.classList.add('hidden');
        return;
      }

      // Show simple suggestions based on query
      const suggestions = getSimpleSuggestions(query);
      if (suggestions.length > 0) {
        displaySearchResults(suggestions, resultsContainer);
      } else {
        displayNoResults(resultsContainer);
      }
    }

    // Get simple hardcoded suggestions
    function getSimpleSuggestions(query) {
      const allSuggestions = [
        { title: 'I Miss The Old Kanye', artist: 'Ye', category: 'unreleased', url: '/artists/ye' },
        { title: 'I Love Kanye', artist: 'Ye', category: 'unreleased', url: '/artists/ye' },
        { title: 'Donda', artist: 'Ye', category: 'unreleased', url: '/artists/ye' },
        { title: 'Yeezus 2', artist: 'Ye', category: 'unreleased', url: '/artists/ye' },
        { title: 'Die Lit', artist: 'Playboi Carti', category: 'unreleased', url: '/artists/playboi-carti' },
        { title: 'Whole Lotta Red', artist: 'Playboi Carti', category: 'unreleased', url: '/artists/playboi-carti' },
        { title: 'Playboi Carti', artist: 'Playboi Carti', category: 'artist', url: '/artists/playboi-carti' },
        { title: 'Ye', artist: 'Ye', category: 'artist', url: '/artists/ye' },
        { title: 'Kanye West', artist: 'Ye', category: 'artist', url: '/artists/ye' }
      ];

      const queryLower = query.toLowerCase();
      return allSuggestions.filter(item =>
        item.title.toLowerCase().includes(queryLower) ||
        item.artist.toLowerCase().includes(queryLower)
      ).slice(0, 5);
    }

    // 显示搜索结果
    function displaySearchResults(results, container) {
      const { searchPath } = getCurrentLanguageAndSearchPath();

      // 限制显示的结果数量，并添加"查看更多"链接
      const displayResults = results.slice(0, 5);
      const hasMore = results.length > 5;

      const html = displayResults.map(result => `
        <a href="${result.url}" class="block px-4 py-3 hover:bg-gray-700 border-b border-gray-600">
          <div class="flex items-center space-x-3">
            <div class="flex-shrink-0">
              <div class="w-10 h-10 bg-primary rounded-lg flex items-center justify-center">
                ${result.type === 'era' ? `
                  <svg class="w-5 h-5 text-black" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M4 3a2 2 0 100 4h12a2 2 0 100-4H4z"/>
                    <path fill-rule="evenodd" d="M3 8h14v7a2 2 0 01-2 2H5a2 2 0 01-2-2V8zm5 3a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z" clip-rule="evenodd"/>
                  </svg>
                ` : `
                  <svg class="w-5 h-5 text-black" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M18 3a1 1 0 00-1.196-.98l-10 2A1 1 0 006 5v9.114A4.369 4.369 0 005 14c-1.657 0-3 .895-3 2s1.343 2 3 2 3-.895 3-2V7.82l8-1.6v5.894A4.37 4.37 0 0015 12c-1.657 0-3 .895-3 2s1.343 2 3 2 3-.895 3-2V3z"/>
                  </svg>
                `}
              </div>
            </div>
            <div class="flex-1 min-w-0">
              <p class="text-sm font-medium text-white truncate">${result.title}</p>
              <p class="text-sm text-gray-400 truncate">${result.artist} • ${result.category}</p>
              ${result.era ? `<p class="text-xs text-gray-500 truncate">${result.era}</p>` : ''}
            </div>
          </div>
        </a>
      `).join('');

      // Add "view more" link
      const viewMoreHtml = hasMore ? `
        <a href="${searchPath}?q=${encodeURIComponent(currentSearchTerm)}" class="block px-4 py-3 text-center text-primary hover:bg-gray-700/50 border-t border-gray-600/30 transition-colors duration-150">
          View all results
        </a>
      ` : '';

      container.innerHTML = html + viewMoreHtml;
      container.classList.remove('hidden');
    }

    // Display no results message
    function displayNoResults(container) {
      container.innerHTML = `
        <div class="px-4 py-6 text-center">
          <p class="text-gray-400">No results found</p>
        </div>
      `;
      container.classList.remove('hidden');
    }

    // Display search error message
    function displaySearchError(container) {
      container.innerHTML = `
        <div class="px-4 py-6 text-center">
          <p class="text-red-400">Search error, please try again</p>
        </div>
      `;
      container.classList.remove('hidden');
    }

    // Debounced search with minimum length requirement
    function debounceSearch(input, resultsContainer) {
      clearTimeout(searchTimeout);
      const query = input.value.trim();

      // 只有当查询长度 >= 2 且与当前搜索词不同时才搜索
      if (query !== currentSearchTerm && query.length >= 2) {
        currentSearchTerm = query;
        searchTimeout = setTimeout(() => {
          performSearch(query, resultsContainer);
        }, 500); // 增加延迟到 500ms 减少请求频率
      } else if (query.length < 2) {
        // 如果查询太短，隐藏结果
        resultsContainer.classList.add('hidden');
        currentSearchTerm = '';
      }
    }

    // 绑定搜索事件
    if (desktopSearchInput) {
      desktopSearchInput.addEventListener('input', () => {
        debounceSearch(desktopSearchInput, desktopSearchResults);
      });

      // 按回车键跳转到搜索页面
      desktopSearchInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
          const query = desktopSearchInput.value.trim();
          const { searchPath } = getCurrentLanguageAndSearchPath();
          if (query) {
            window.location.href = `${searchPath}?q=${encodeURIComponent(query)}`;
          } else {
            // 空查询也跳转到搜索页面，但不带参数
            window.location.href = searchPath;
          }
        }
      });

      // 点击外部关闭搜索结果
      document.addEventListener('click', (e) => {
        if (!desktopSearchInput.contains(e.target) && !desktopSearchResults.contains(e.target)) {
          desktopSearchResults.classList.add('hidden');
        }
      });
    }

    if (mobileSearchInput) {
      mobileSearchInput.addEventListener('input', () => {
        debounceSearch(mobileSearchInput, mobileSearchResults);
      });

      // 按回车键跳转到搜索页面
      mobileSearchInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
          const query = mobileSearchInput.value.trim();
          const { searchPath } = getCurrentLanguageAndSearchPath();
          if (query) {
            window.location.href = `${searchPath}?q=${encodeURIComponent(query)}`;
          } else {
            // 空查询也跳转到搜索页面，但不带参数
            window.location.href = searchPath;
          }
        }
      });

      // 点击外部关闭搜索结果
      document.addEventListener('click', (e) => {
        if (!mobileSearchInput.contains(e.target) && !mobileSearchResults.contains(e.target)) {
          mobileSearchResults.classList.add('hidden');
        }
      });
    }
  }

  // 初始化搜索功能
  document.addEventListener('DOMContentLoaded', initializeSearch);

  // 监听各种可能的页面导航事件
  if (typeof window !== 'undefined') {
    // Astro 页面导航事件（如果支持）
    document.addEventListener('astro:page-load', initializeSearch);
    document.addEventListener('astro:after-swap', initializeSearch);

    // 浏览器导航事件
    window.addEventListener('popstate', () => {
      setTimeout(initializeSearch, 100);
    });

    // 监听 pushState 和 replaceState（用于客户端路由）
    const originalPushState = history.pushState;
    const originalReplaceState = history.replaceState;

    history.pushState = function() {
      originalPushState.apply(history, arguments);
      setTimeout(initializeSearch, 100);
    };

    history.replaceState = function() {
      originalReplaceState.apply(history, arguments);
      setTimeout(initializeSearch, 100);
    };

    // 定期检查搜索框是否存在但未初始化
    let initCheckInterval = setInterval(() => {
      const searchInput = document.getElementById('globalSearch');
      if (searchInput && !searchInput.hasAttribute('data-search-initialized')) {
        initializeSearch();
      }
    }, 1000);

    // 10秒后停止定期检查以节省资源
    setTimeout(() => {
      if (initCheckInterval) {
        clearInterval(initCheckInterval);
        initCheckInterval = null;
      }
    }, 10000);
  }
</script>
