---
// Search Page - Simple Version
import MainLayout from '../../layouts/MainLayout.astro';

// Set current language to English
const currentLang = 'en';

// Get search query from URL
const url = new URL(Astro.request.url);
const searchQuery = url.searchParams.get('q') || '';

// SEO data
const pageTitle = searchQuery ? `Search: ${searchQuery} - TrackerHive` : 'Search - TrackerHive';
const pageDescription = searchQuery
  ? `Search results for "${searchQuery}" - Discover music on TrackerHive`
  : 'Search for your favorite music, artists, and albums on TrackerHive';
---

<MainLayout 
  title={pageTitle}
  description={pageDescription}
  currentLang={currentLang}
>
  <div class="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-900">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">

      <!-- Search Results Container -->
      <div id="searchResultsContainer" class="min-h-[400px]">
        <!-- Content will be dynamically loaded here -->
      </div>
    </div>
  </div>

  <script>
    // Mock data for search results
    const mockResults = [
      {
        id: "amazing",
        name: "✨ Amazing [V2]",
        artist: "Kanye West",
        category: "Unreleased",
        era: "808s & Heartbreak",
        type: "track",
        notes: "The-Dream reference track for the song. Leaked via a unofficial mixtape entitled LoveTape: The Demo's.",
        quality: "CD Quality",
        availableLength: "Full"
      },
      {
        id: "heartless",
        name: "Heartless [V2]",
        artist: "Kanye West", 
        category: "Unreleased",
        era: "808s & Heartbreak",
        type: "track",
        notes: "Early version of \"Heartless\" with way rougher mixing, no 'Hey' sound effects on the second verse and mumble on the third verse.",
        quality: "High Quality",
        availableLength: "OG File"
      },
      {
        id: "808s-heartbreak",
        name: "808s & Heartbreak",
        artist: "Kanye West",
        category: "Unreleased",
        era: "808s & Heartbreak",
        type: "era",
        notes: "Following the death of his mother due to complications after cosmetic surgery, his relationship with fiancé Alexis Phifer finally ending for good.",
        quality: "Various",
        availableLength: "Various"
      },
      {
        id: "robocop",
        name: "RoboCop [V1]",
        artist: "Kanye West",
        category: "Unreleased",
        era: "808s & Heartbreak",
        type: "track",
        notes: "Mumble version of \"RoboCop\". Has a section where Kanye interpolates \"Technologic\" by Daft Punk.",
        quality: "Lossless",
        availableLength: "OG File"
      },
      {
        id: "die-lit",
        name: "Die Lit",
        artist: "Playboi Carti",
        category: "Released",
        era: "Die Lit Era",
        type: "era",
        notes: "Playboi Carti's second studio album, featuring hits like \"Shoota\" and \"R.I.P.\"",
        quality: "Various",
        availableLength: "Various"
      }
    ];

    const searchResultsContainer = document.getElementById('searchResultsContainer');
    let currentQuery = '';

    // Get search query from URL
    const urlParams = new URLSearchParams(window.location.search);
    const initialQuery = urlParams.get('q') || '';

    // Simple search function
    function performSearch(query) {
      // Update current query
      currentQuery = query;

      // Update URL
      const newUrl = new URL(window.location);
      if (query.trim()) {
        newUrl.searchParams.set('q', query.trim());
      } else {
        newUrl.searchParams.delete('q');
      }
      window.history.pushState({}, '', newUrl);

      // Handle empty query
      if (!query.trim()) {
        showDefaultState();
        return;
      }

      showLoadingState();

      // Simulate API call delay
      setTimeout(() => {
        const results = mockResults.filter(item =>
          item.name.toLowerCase().includes(query.toLowerCase()) ||
          item.artist.toLowerCase().includes(query.toLowerCase()) ||
          item.category.toLowerCase().includes(query.toLowerCase()) ||
          item.era.toLowerCase().includes(query.toLowerCase())
        );

        if (results.length > 0) {
          displaySearchResults(results, query);
        } else {
          displayNoResults(query);
        }
      }, 500);
    }

    // Show default state
    function showDefaultState() {
      searchResultsContainer.innerHTML = `
        <div class="text-center py-16">
          <div class="mb-8">
            <svg class="mx-auto h-20 w-20 text-gray-600 mb-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
            </svg>
            <h2 class="text-2xl font-bold text-white mb-3">Search Music</h2>
            <p class="text-gray-400 mb-8 max-w-md mx-auto">
              Find your favorite songs, artists, and albums
            </p>
          </div>
          
          <div class="max-w-2xl mx-auto">
            <h3 class="text-lg font-semibold text-white mb-4">Popular Searches</h3>
            <div class="flex flex-wrap gap-2 justify-center">
              <button onclick="performSearch('Kanye West')" class="px-4 py-2 bg-gray-800 hover:bg-gray-700 text-gray-300 rounded-full text-sm transition-colors duration-200">Kanye West</button>
              <button onclick="performSearch('Playboi Carti')" class="px-4 py-2 bg-gray-800 hover:bg-gray-700 text-gray-300 rounded-full text-sm transition-colors duration-200">Playboi Carti</button>
              <button onclick="performSearch('808s')" class="px-4 py-2 bg-gray-800 hover:bg-gray-700 text-gray-300 rounded-full text-sm transition-colors duration-200">808s</button>
              <button onclick="performSearch('Heartbreak')" class="px-4 py-2 bg-gray-800 hover:bg-gray-700 text-gray-300 rounded-full text-sm transition-colors duration-200">Heartbreak</button>
              <button onclick="performSearch('Die Lit')" class="px-4 py-2 bg-gray-800 hover:bg-gray-700 text-gray-300 rounded-full text-sm transition-colors duration-200">Die Lit</button>
            </div>
          </div>
        </div>
      `;
    }

    // Show loading state
    function showLoadingState() {
      searchResultsContainer.innerHTML = `
        <div class="text-center py-16">
          <div class="inline-flex flex-col items-center">
            <div class="relative mb-6">
              <svg class="animate-spin h-12 w-12 text-primary" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
            </div>
            <h3 class="text-xl font-semibold text-white mb-2">Searching...</h3>
            <p class="text-gray-400">Finding the best results for you</p>
          </div>
        </div>
      `;
    }

    // Display search results
    function displaySearchResults(results, query) {
      const resultsHtml = `
        <div class="mb-8">
          <p class="text-gray-400 text-lg">
            Found <span class="text-white font-semibold">${results.length}</span> results for "<span class="text-primary font-semibold">${query}</span>"
          </p>
        </div>

        <div class="grid gap-4">
          ${results.map(result => `
            <div class="group block bg-gray-800/50 hover:bg-gray-800 rounded-xl p-6 transition-all duration-300 border border-gray-700/50 hover:border-primary/30">
              <div class="flex items-start space-x-5">
                <div class="flex-shrink-0">
                  <div class="w-14 h-14 bg-gradient-to-br from-primary to-primary/80 rounded-xl flex items-center justify-center shadow-lg group-hover:scale-105 transition-transform duration-300">
                    ${result.type === 'era' ? `
                      <svg class="w-7 h-7 text-black" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M4 3a2 2 0 100 4h12a2 2 0 100-4H4z"/>
                        <path fill-rule="evenodd" d="M3 8h14v7a2 2 0 01-2 2H5a2 2 0 01-2-2V8zm5 3a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z" clip-rule="evenodd"/>
                      </svg>
                    ` : `
                      <svg class="w-7 h-7 text-black" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M18 3a1 1 0 00-1.196-.98l-10 2A1 1 0 006 5v9.114A4.369 4.369 0 005 14c-1.657 0-3 .895-3 2s1.343 2 3 2 3-.895 3-2V7.82l8-1.6v5.894A4.37 4.37 0 0015 12c-1.657 0-3 .895-3 2s1.343 2 3 2 3-.895 3-2V3z"/>
                      </svg>
                    `}
                  </div>
                </div>
                <div class="flex-1 min-w-0">
                  <h3 class="text-xl font-bold text-white mb-2 group-hover:text-primary transition-colors duration-300">${result.name}</h3>
                  <p class="text-gray-300 mb-3 text-lg">${result.artist}</p>
                  <div class="flex items-center flex-wrap gap-3 text-sm mb-3">
                    <span class="px-3 py-1 bg-gray-700/70 text-gray-300 rounded-full font-medium">${result.category}</span>
                    ${result.era ? `<span class="text-gray-400">${result.era}</span>` : ''}
                    <span class="px-3 py-1 bg-primary/20 text-primary rounded-full font-medium capitalize">${result.type === 'era' ? 'Album' : 'Track'}</span>
                  </div>
                  ${result.notes ? `<p class="text-gray-400 line-clamp-2 leading-relaxed">${result.notes}</p>` : ''}
                </div>
              </div>
            </div>
          `).join('')}
        </div>
      `;

      searchResultsContainer.innerHTML = resultsHtml;
    }

    // Display no results
    function displayNoResults(query) {
      searchResultsContainer.innerHTML = `
        <div class="text-center py-16">
          <div class="mb-8">
            <svg class="mx-auto h-16 w-16 text-gray-500 mb-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 0112 15c-2.34 0-4.47-.881-6.08-2.33"></path>
            </svg>
            <h3 class="text-xl font-bold text-white mb-3">No Results Found</h3>
            <p class="text-gray-400 mb-8 max-w-md mx-auto">
              No content found for "<span class="text-primary font-medium">${query}</span>"
            </p>
          </div>
          
          <div class="max-w-lg mx-auto">
            <div class="bg-gray-800/50 rounded-xl p-6 border border-gray-700/50">
              <h4 class="text-white font-semibold mb-4">Try these suggestions:</h4>
              <ul class="text-gray-400 space-y-2 text-left">
                <li class="flex items-center">
                  <svg class="w-4 h-4 text-primary mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                  </svg>
                  Check your spelling
                </li>
                <li class="flex items-center">
                  <svg class="w-4 h-4 text-primary mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                  </svg>
                  Try different keywords
                </li>
                <li class="flex items-center">
                  <svg class="w-4 h-4 text-primary mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                  </svg>
                  Use more general search terms
                </li>
              </ul>
            </div>
          </div>
        </div>
      `;
    }

    // Initialize page
    document.addEventListener('DOMContentLoaded', function() {
      if (initialQuery) {
        performSearch(initialQuery);
      } else {
        showDefaultState();
      }

      // Listen for URL changes (when user uses browser back/forward)
      window.addEventListener('popstate', function() {
        const urlParams = new URLSearchParams(window.location.search);
        const query = urlParams.get('q') || '';
        performSearch(query);
      });
    });

    // Make performSearch available globally for button clicks and header search
    window.performSearch = performSearch;
  </script>
</MainLayout>
