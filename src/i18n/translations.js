// 阿拉伯语翻译
export const ar = {
  common: {
    home: "الرئيسية",
    homeTitle: "اكتشف الموسيقى المفضلة التالية",
    artists: "الفنانين",
    categories: "الفئات",
    search: "بحث",
    recentlyPlayed: "تم تشغيلها مؤخرًا",
    recentlyPlayedDesc: "المسارات التي تم تشغيلها مؤخرًا",
    track: "المسار",
    album: "الألبوم",
    status: {
      title: "الحالة",
      official: "رسمي",
      leaked: "مخترق",
      unreleased: "غير منشور",
      unknown: "غير معروف"
    },
    playedAt: "تم التشغيل في",
    clearHistory: "مسح السجل",
    played: "تم تشغيله",
    lang: "اللغة",
    unknown: "غير معروف",
    latestUpdates: "أحدث تحديثات متتبع يي",
    topArtists: "الفنانين الرئيسية في متتبع يي",
    seeAll: "عرض الكل",
    explore: "استكشف متتبع يي - أكثر كتالوج موسيقي شمولاً ليي (كاني ويست) وابق على اطلاع بأحدث الإصدارات",
    browse: "تصفح",
    exploreArtist: "استكشف موسيقى متتبع يي",
    browseAllArtists: "تصفح جميع الفنانين",
    comingSoon: "قريبًا",
    contentBeingCurated: "نعمل حاليًا على تنظيم المحتوى للفئة",
    checkBackSoon: "تحقق مرة أخرى قريبًا للحصول على التحديثات!",
    noDataAvailable: "لا توجد بيانات متاحة لهذه الفئة حتى الآن.",
    checkBackLater: "يرجى التحقق مرة أخرى لاحقًا.",
    displayModeError: "خطأ: لم يتم تكوين وضع العرض أو لم يتم العثور على المكون.",
    displayModeErrorDetail: "تعذر تحديد كيفية عرض المحتوى لهذه الفئة.",
    otherCategories: "فئات أخرى",
    noRecentlyPlayed: "لا توجد مسارات تم تشغيلها مؤخرًا.",
    noRecentTracks: "لا توجد مسارات تم تشغيلها مؤخرًا.",
    tracksWillAppear: "ستظهر المسارات هنا",
    pageNotFound: "الصفحة غير موجودة",
    pageNotFoundDesc: "الصفحة التي تبحث عنها غير موجودة أو تم نقلها.",
    backToHome: "العودة إلى الصفحة الرئيسية",
  },
  search: {
    placeholder: "البحث عن الأغاني والفنانين والألبومات...",
    results: "نتائج البحث",
    noResults: "لم يتم العثور على نتائج",
    searchMusic: "البحث في الموسيقى",
    found: "تم العثور على",
    suggestions: "اقتراحات البحث",
    popularSearches: "عمليات البحث الشائعة",
    startSearching: "ابدأ البحث عن الموسيقى",
    searchDescription: "أدخل اسم الأغنية أو الفنان أو الألبوم في مربع البحث أعلاه لاكتشاف موسيقاك المفضلة",
    tryDifferentKeywords: "جرب كلمات مفتاحية مختلفة أو تحقق من الإملاء",
    searchTips: "نصائح البحث:",
    tipShorter: "• جرب كلمات مفتاحية أقصر",
    tipSpelling: "• تحقق من صحة الإملاء",
    tipEnglish: "• استخدم الأسماء الإنجليزية للأغاني أو الفنانين",
    searchError: "حدث خطأ أثناء البحث، يرجى المحاولة مرة أخرى لاحقاً",
  },
  categories: {
    released: "الإصدارات",
    "released.description": "تصفح الألبومات والأغاني الرسمية التي أصدرها الفنان طوال مسيرته المهنية.",
    unreleased: "غير منشور",
    "unreleased.description": "استكشف المسارات المسربة وغير المنشورة من مختلف فترات مسيرة الفنان.",
    stems: "المقاطع",
    "stems.description": "الوصول إلى مكونات الصوت المعزولة والآلات وغيرها من مكونات المسار من أعمال الفنان.",
    remixes: "ريمكسات",
    "remixes.description": "اكتشف الريمكسات الرسمية وغير الرسمية لمسارات الفنان.",
    tracklists: "قوائم المسارات",
    "tracklists.description": "عرض مجموعات من قوائم الألبومات والمشاريع من مسيرة الفنان.",
    recent: "الأخيرة",
    "recent.description": "تحقق من المسارات المضافة أو المحدثة مؤخرًا في كتالوج الفنان.",
    art: "الفن",
    "art.description": "عرض الأعمال الفنية والوسائط المرئية التي أنشأها الفنان أو المرتبطة به.",
    "best-of": "الأفضل",
    "best-of.description": "استكشف مجموعات منسقة من أفضل أعمال الفنان عبر مختلف العصور والأساليب.",
    "album-copies": "نسخ الألبوم",
    "album-copies.description": "الوصول إلى إصدارات ونسخ بديلة من إصدارات الألبوم الرسمية للفنان.",
    fakes: "المزيفة",
    "fakes.description": "تعرف على المسارات المزيفة أو المنسوبة خطأً المتداولة عبر الإنترنت وتحديدها.",
    grails: "الكنوز",
    "grails.description": "اكتشف المسارات والمقتطفات غير المنشورة الأكثر طلبًا.",
    groupbuys: "المشتريات الجماعية",
    "groupbuys.description": "معلومات حول جهود المجتمع لشراء موسيقى غير منشورة.",
    misc: "متنوعات",
    "misc.description": "محتوى متنوع لا يندرج ضمن فئات أخرى.",
    special: "خاص",
    "special.description": "مجموعات خاصة ومحتوى فريد متعلق بمسيرة الفنان.",
    "worst-of": "الأسوأ",
    "worst-of.description": "مجموعة من أعمال الفنان الأقل شعبية أو الأكثر إثارة للجدل.",
    snippets: "مقتطفات",
    "snippets.description": "معاينات قصيرة ومقاطع من المسارات غير المنشورة أو القادمة.",
    leaks: "تسريبات",
    "leaks.description": "إصدارات غير مصرح بها ومسارات مسربة من كتالوج الفنان."
  },
  artist: {
    albums: "الألبومات",
    tracks: "المسارات",
    about: "حول",
    relatedArtists: "فنانين ذوي صلة",
    comingSoon: "قريبًا",
    noData: "لا توجد بيانات متاحة",
    alsoKnownAs: "يُعرف أيضًا باسم",
    categories: "التصنيفات",
    yeDescription: "كانيي عمري ويست، المعروف مهنيًا باسم يي تراكر (سابقًا كانيي ويست)، هو مغني راب أمريكي ومنتج موسيقي ومصمم أزياء. مع مسيرة مهنية امتدت لأكثر من عقدين، أصبح واحدًا من أكثر الفنانين تأثيرًا وإثارة للجدل في جيله، معروف بألبومات مثل \"ذا كوليدج دروب آوت\"، \"ماي بيوتيفول دارك تويستد فانتازي\"، و\"دوندا\".",
    playboiCartiDescription: "جوردان تيريل كارتر، المعروف مهنيًا باسم بلايبوي كارتي، هو مغني راب أمريكي. وهو معروف بأسلوبه الموسيقي التجريبي وأزياءه القوطية وشخصيته العامة الغامضة.",
    released: "الإصدارات",
    unreleased: "غير منشور",
    stems: "المقاطع",
    remixes: "ريمكسات",
    tracklists: "قوائم المسارات",
    recent: "الأخيرة",
    art: "الفن",
    "best-of": "الأفضل",
    "album-copies": "نسخ الألبوم",
    fakes: "المزيفة",
    grails: "الكنوز",
    groupbuys: "المشتريات الجماعية",
    misc: "متنوعات",
    special: "خاص",
    "worst-of": "الأسوأ",
    snippets: "مقتطفات",
    leaks: "تسريبات"
  },
  track: {
    play: "تشغيل",
    pause: "إيقاف مؤقت",
    addToPlaylist: "إضافة إلى قائمة التشغيل",
    share: "مشاركة",
    lyrics: "كلمات الأغاني",
    releasedOn: "تم إصداره في",
    duration: "المدة",
    track: "المسار",
    length: "المدة",
    quality: "الجودة/متوفر",
    audioNotSupported: "متصفحك لا يدعم تشغيل الصوت.",
    showNotes: "عرض الملاحظات",
    trackList: "قائمة المسارات",
    playbackFailed: "فشل التشغيل، يرجى المحاولة لاحقًا"
  },
  footer: {
    copyright: " 2025 AITrackerHive. جميع الحقوق محفوظة.",
    privacyPolicy: "سياسة الخصوصية",
    termsOfService: "شروط الخدمة",
    contactUs: "اتصل بنا"
  },
  navigation: {
    title: "التنقل",
    home: "الرئيسية",
    artists: "الفنانين",
    albums: "الألبومات",
    tracks: "المسارات"
  },
  resources: {
    title: "الموارد",
    about: "حول",
    faq: "الأسئلة الشائعة",
    contact: "اتصل"
  },
  legal: {
    title: "قانوني",
    privacyPolicy: "سياسة الخصوصية",
    termsOfUse: "شروط الاستخدام",
    dmca: "DMCA"
  }
};

// 英语翻译
export const en = {
  common: {
    home: "Home",
    homeTitle: "Ye Tracker - Discover Unreleased Kanye West Music",
    artists: "Artists",
    categories: "Categories",
    search: "Search",
    recentlyPlayed: "Recently Played",
    recentlyPlayedDesc: "Your recently played tracks",
    track: "Track",
    album: "Album",
    status: {
      title: "Status",
      official: "Official",
      leaked: "Leaked",
      unreleased: "Unreleased",
      unknown: "Unknown"
    },
    playedAt: "Played At",
    clearHistory: "Clear History",
    played: "played",
    lang: "Language",
    unknown: "Unknown",
    latestUpdates: "Latest Yetracker Unreleased Updates",
    topArtists: "Top Artists",
    seeAll: "See all",
    explore: "The ultimate yetracker platform for unreleased music. Discover kanye west tracker content and art collections with our comprehensive database.",
    browse: "Browse",
    exploreArtist: "Explore Yetracker Unreleased",
    browseAllArtists: "Browse All Artists",
    comingSoon: "Coming Soon",
    contentBeingCurated: "We're currently working on curating content for the",
    checkBackSoon: "category. Check back soon for updates!",
    noDataAvailable: "No data available for this category yet.",
    checkBackLater: "Please check back later.",
    displayModeError: "Error: Display mode not configured or component not found.",
    displayModeErrorDetail: "Could not determine how to display content for this category.",
    otherCategories: "Other Categories",
    noRecentlyPlayed: "No recently played tracks yet.",
    noRecentTracks: "No recently played tracks yet.",
    tracksWillAppear: "Tracks will appear here",
    pageNotFound: "Page Not Found",
    pageNotFoundDesc: "The page you're looking for doesn't exist or has been moved.",
    backToHome: "Back to Home",
  },
  search: {
    placeholder: "Search songs, artists, albums...",
    results: "Search Results",
    noResults: "No results found",
    searchMusic: "Search Music",
    found: "Found",
    suggestions: "Search Suggestions",
    popularSearches: "Popular Searches",
    startSearching: "Start Searching Music",
    searchDescription: "Enter a song name, artist, or album in the search box above to discover your favorite music",
    tryDifferentKeywords: "Try different keywords or check spelling",
    searchTips: "Search Tips:",
    tipShorter: "• Try shorter keywords",
    tipSpelling: "• Check spelling accuracy",
    tipEnglish: "• Use English names for songs or artists",
    searchError: "An error occurred while searching, please try again later",
  },
  categories: {
    released: "Released",
    "released.description": "Browse official albums and singles released by the artist throughout their career.",
    unreleased: "Unreleased",
    "unreleased.description": "Explore leaked and unreleased tracks from various eras of the artist's career.",
    stems: "Stems",
    "stems.description": "Access isolated vocal, instrumental, and other track components from the artist's discography.",
    remixes: "Remixes",
    "remixes.description": "Discover official and unofficial remixes of the artist's tracks.",
    tracklists: "Tracklists",
    "tracklists.description": "View compilations of album and project tracklists from the artist's career.",
    recent: "Recent",
    "recent.description": "Check out recently added or updated tracks in the artist's catalog.",
    art: "Art",
    "art.description": "View artwork and visual media created by or associated with the artist.",
    "best-of": "Best Of",
    "best-of.description": "Explore curated collections of the artist's best work across different eras and styles.",
    "album-copies": "Album Copies",
    "album-copies.description": "Access alternative versions and copies of the artist's official album releases.",
    fakes: "Fakes",
    "fakes.description": "Learn about and identify fake or misattributed tracks circulating online.",
    grails: "Grails",
    "grails.description": "Discover the most sought-after unreleased tracks and snippets.",
    groupbuys: "Groupbuys",
    "groupbuys.description": "Information about community efforts to purchase unreleased music.",
    misc: "Misc",
    "misc.description": "Miscellaneous content that doesn't fit into other categories.",
    special: "Special",
    "special.description": "Special collections and unique content related to the artist's career.",
    "worst-of": "Worst Of",
    "worst-of.description": "A collection of the artist's least popular or most controversial works.",
    snippets: "Snippets",
    "snippets.description": "Short previews and clips of unreleased or upcoming tracks.",
    leaks: "Leaks",
    "leaks.description": "Unauthorized releases and leaked tracks from the artist's catalog."
  },
  artist: {
    albums: "Albums",
    tracks: "Tracks",
    about: "About",
    relatedArtists: "Related Artists",
    comingSoon: "Coming Soon",
    noData: "No data available",
    alsoKnownAs: "Also known as",
    categories: "Categories",
    yeDescription: "Kanye Omari West, known professionally as Yetracker (formerly Kanye West), is an American rapper, record producer, and fashion designer. Our ye tracker provides comprehensive updates on his albums like \"The College Dropout,\" \"My Beautiful Dark Twisted Fantasy,\" and \"Donda.\" Access unreleased music, rare demos, and exclusive content through our advanced ye tracker system. The ye tracker offers the most complete database for fans worldwide.",
    playboiCartiDescription: "Jordan Terrell Carter, known professionally as Playboi Carti, is an American rapper and singer. Our carti tracker provides comprehensive updates on his experimental musical style, gothic fashion, and mysterious public persona. Access exclusive content through our advanced carti tracker system, featuring unreleased tracks, rare demos, and leaked music. The carti tracker offers the most complete database for Playboi Carti fans worldwide.",
    released: "Released",
    unreleased: "Unreleased",
    stems: "Stems",
    remixes: "Remixes",
    tracklists: "Tracklists",
    recent: "Recent",
    art: "Art",
    "best-of": "Best Of",
    "album-copies": "Album Copies",
    fakes: "Fakes",
    grails: "Grails",
    groupbuys: "Groupbuys",
    misc: "Misc",
    special: "Special",
    "worst-of": "Worst Of",
    snippets: "Snippets",
    leaks: "Leaks"
  },
  track: {
    play: "Play",
    pause: "Pause",
    addToPlaylist: "Add to Playlist",
    share: "Share",
    lyrics: "Lyrics",
    releasedOn: "Released on",
    duration: "Duration",
    track: "Track",
    length: "Length",
    quality: "Quality/Available",
    audioNotSupported: "Your browser does not support audio playback.",
    showNotes: "Show Notes",
    trackList: "Tracks",
    playbackFailed: "Playback failed, please try again later"
  },
  footer: {
    copyright: " 2025 AITrackerHive. All rights reserved.",
    privacyPolicy: "Privacy Policy",
    termsOfService: "Terms of Service",
    contactUs: "Contact Us"
  },
  navigation: {
    title: "Navigation",
    home: "Home",
    artists: "Artists",
    albums: "Albums",
    tracks: "Tracks"
  },
  resources: {
    title: "Resources",
    about: "About",
    faq: "FAQ",
    contact: "Contact"
  },
  legal: {
    title: "Legal",
    privacyPolicy: "Privacy Policy",
    termsOfUse: "Terms of Use",
    dmca: "DMCA"
  }
};

// 葡萄牙语翻译
export const pt = {
  common: {
    home: "Início",
    homeTitle: "Descubra Sua Próxima Música Favorita",
    artists: "Artistas",
    categories: "Categorias",
    search: "Pesquisar",
    recentlyPlayed: "Reproduzidas Recentemente",
    recentlyPlayedDesc: "Suas faixas reproduzidas recentemente",
    track: "Faixa",
    album: "Álbum",
    status: {
      title: "Status",
      official: "Oficial",
      leaked: "Vazado",
      unreleased: "Não Lançado",
      unknown: "Desconhecido"
    },
    playedAt: "Reproduzido em",
    clearHistory: "Limpar Histórico",
    played: "reproduzido",
    lang: "Idioma",
    unknown: "Desconhecido",
    latestUpdates: "Últimas Atualizações do Ye Tracker",
    topArtists: "Principais Artistas",
    seeAll: "Ver tudo",
    explore: "Explore o Ye tracker - o catálogo de música mais completo para Ye (Kanye West) e mantenha-se atualizado com os lançamentos mais recentes",
    browse: "Navegar",
    exploreArtist: "Explorar Música do Ye Tracker",
    browseAllArtists: "Navegar por Todos os Artistas",
    comingSoon: "Em Breve",
    contentBeingCurated: "Estamos trabalhando na curadoria de conteúdo para a categoria",
    checkBackSoon: "Volte em breve para atualizações!",
    noDataAvailable: "Nenhum dado disponível para esta categoria ainda.",
    checkBackLater: "Por favor, verifique novamente mais tarde.",
    displayModeError: "Erro: Modo de exibição não configurado ou componente não encontrado.",
    displayModeErrorDetail: "Não foi possível determinar como exibir o conteúdo para esta categoria.",
    otherCategories: "Outras Categorias",
    noRecentlyPlayed: "Ainda não há faixas reproduzidas recentemente.",
    noRecentTracks: "Ainda não há faixas reproduzidas recentemente.",
    tracksWillAppear: "As faixas aparecerão aqui",
    pageNotFound: "Página Não Encontrada",
    pageNotFoundDesc: "A página que você está procurando não existe ou foi movida.",
    backToHome: "Voltar para Início",
  },
  search: {
    placeholder: "Pesquisar músicas, artistas, álbuns...",
    results: "Resultados da Pesquisa",
    noResults: "Nenhum resultado encontrado",
    searchMusic: "Pesquisar Música",
    found: "Encontrado",
    suggestions: "Sugestões de Pesquisa",
    popularSearches: "Pesquisas Populares",
    startSearching: "Comece a Pesquisar Música",
    searchDescription: "Digite o nome de uma música, artista ou álbum na caixa de pesquisa acima para descobrir sua música favorita",
    tryDifferentKeywords: "Tente palavras-chave diferentes ou verifique a ortografia",
    searchTips: "Dicas de Pesquisa:",
    tipShorter: "• Tente palavras-chave mais curtas",
    tipSpelling: "• Verifique a precisão da ortografia",
    tipEnglish: "• Use nomes em inglês para músicas ou artistas",
    searchError: "Ocorreu um erro durante a pesquisa, tente novamente mais tarde",
  },
  categories: {
    released: "Lançados",
    "released.description": "Navegue por álbuns e singles oficiais lançados pelo artista ao longo de sua carreira.",
    unreleased: "Não Lançados",
    "unreleased.description": "Explore faixas vazadas e não lançadas de várias eras da carreira do artista.",
    stems: "Stems",
    "stems.description": "Acesse componentes isolados de vocais, instrumentais e outras partes das faixas da discografia do artista.",
    remixes: "Remixes",
    "remixes.description": "Descubra remixes oficiais e não oficiais das faixas do artista.",
    tracklists: "Tracklists",
    "tracklists.description": "Veja compilações de listas de faixas de álbuns e projetos da carreira do artista.",
    recent: "Recentes",
    "recent.description": "Confira faixas recentemente adicionadas ou atualizadas no catálogo do artista.",
    art: "Arte",
    "art.description": "Veja obras de arte e mídia visual criadas pelo artista ou associadas a ele.",
    "best-of": "Melhores",
    "best-of.description": "Explore coleções curadas dos melhores trabalhos do artista em diferentes eras e estilos.",
    "album-copies": "Cópias de Álbuns",
    "album-copies.description": "Acesse versões alternativas e cópias dos lançamentos oficiais de álbuns do artista.",
    fakes: "Falsificações",
    "fakes.description": "Aprenda sobre e identifique faixas falsas ou atribuídas incorretamente circulando online.",
    grails: "Raridades",
    "grails.description": "Descubra as faixas e trechos não lançados mais procurados.",
    groupbuys: "Compras em Grupo",
    "groupbuys.description": "Informações sobre esforços da comunidade para comprar música não lançada.",
    misc: "Diversos",
    "misc.description": "Conteúdo diverso que não se encaixa em outras categorias.",
    special: "Especial",
    "special.description": "Coleções especiais e conteúdo único relacionado à carreira do artista.",
    "worst-of": "Piores",
    "worst-of.description": "Uma coleção dos trabalhos menos populares ou mais controversos do artista.",
    snippets: "Trechos",
    "snippets.description": "Prévias curtas e clipes de faixas não lançadas ou futuras.",
    leaks: "Vazamentos",
    "leaks.description": "Lançamentos não autorizados e faixas vazadas do catálogo do artista."
  },
  artist: {
    albums: "Álbuns",
    tracks: "Faixas",
    about: "Sobre",
    relatedArtists: "Artistas Relacionados",
    comingSoon: "Em Breve",
    noData: "Nenhum dado disponível",
    alsoKnownAs: "Também conhecido como",
    categories: "Categorias",
    yeDescription: "Kanye Omari West, conhecido profissionalmente como Yetracker (anteriormente Kanye West), é um rapper, produtor musical e estilista americano. Com uma carreira que abrange mais de duas décadas, ele se tornou um dos artistas mais influentes e controversos de sua geração, conhecido por álbuns como \"The College Dropout\", \"My Beautiful Dark Twisted Fantasy\" e \"Donda\".",
    playboiCartiDescription: "Jordan Terrell Carter, conhecido profissionalmente como Playboi Carti, é um rapper e cantor americano. Ele é reconhecido por seu estilo musical experimental, moda gótica e persona pública misteriosa.",
    released: "Lançados",
    unreleased: "Não lançados",
    stems: "Stems",
    remixes: "Remixes",
    tracklists: "Tracklists",
    recent: "Recentes",
    art: "Arte",
    "best-of": "Melhores",
    "album-copies": "Cópias de Álbuns",
    fakes: "Falsificações",
    grails: "Raridades",
    groupbuys: "Compras Coletivas",
    misc: "Diversos",
    special: "Especial",
    "worst-of": "Piores",
    snippets: "Trechos",
    leaks: "Vazamentos"
  },
  track: {
    play: "Reproduzir",
    pause: "Pausar",
    addToPlaylist: "Adicionar à Playlist",
    share: "Compartilhar",
    lyrics: "Letras",
    releasedOn: "Lançado em",
    duration: "Duração",
    track: "Faixa",
    length: "Duração",
    quality: "Qualidade/Disponível",
    audioNotSupported: "Seu navegador não suporta a reprodução de áudio.",
    showNotes: "Mostrar Notas",
    trackList: "Lista de Faixas",
    playbackFailed: "Falha na reprodução, tente novamente mais tarde"
  },
  footer: {
    copyright: " 2025 AITrackerHive. Todos os direitos reservados.",
    privacyPolicy: "Política de Privacidade",
    termsOfService: "Termos de Serviço",
    contactUs: "Contate-nos"
  },
  navigation: {
    title: "Navegação",
    home: "Início",
    artists: "Artistas",
    albums: "Álbuns",
    tracks: "Faixas"
  },
  resources: {
    title: "Recursos",
    about: "Sobre",
    faq: "Perguntas Frequentes",
    contact: "Contato"
  },
  legal: {
    title: "Legal",
    privacyPolicy: "Política de Privacidade",
    termsOfUse: "Termos de Uso",
    dmca: "DMCA"
  }
};
