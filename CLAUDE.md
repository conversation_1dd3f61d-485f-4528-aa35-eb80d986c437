# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

TrackerHive is a multilingual music artist tracking platform built with Astro 5.6, optimized for tracking Ye (Kanye West) and Playboi Carti. The website provides track listings, artwork galleries, and audio playback functionality with SEO optimization.

## Technology Stack

- **Framework**: Astro 5.6 (static site generation)
- **Styling**: Tailwind CSS 3.4 with custom theme
- **UI Components**: React 19.1 components with Astro integration
- **Languages**: TypeScript with strict config
- **Deployment**: Vercel with static output
- **Package Manager**: npm

## Core Development Commands

```bash
# Development
npm run dev                    # Start dev server at localhost:4321
npm run build                  # Build production site to ./dist/
npm run preview                # Preview build locally

# Data Management
npm run extract-albums         # Extract and organize album data from HTML sources
npm run download-images        # Download and optimize images from external sources

# SEO & Content Generation
npm run generate-og            # Generate Open Graph images for artists/categories
npm run generate-sitemap       # Generate XML sitemaps for all pages
npm run generate-rss           # Generate RSS feeds for artist updates
npm run generate-seo           # Run all SEO generation commands

# Content Submission
npm run indexnow               # Submit URLs to IndexNow for search indexing
npm run indexnow-integration   # Integrate with IndexNow after build

# Analysis & Maintenance
npm run analyze-404            # Find and fix 404 errors in redirects
npm run verify-redirects       # Verify Vercel redirect configurations

# Build Process
npm run prebuild               # Runs extract-albums and generate-seo
npm run postbuild              # Runs indexnow-integration
```

## Architecture Overview

### Multilingual Support
- **Languages**: English (default), Arabic (RTL), Portuguese
- **Routing**: Language prefixes for non-English (`/ar/*`, `/pt/*`)
- **Middleware**: Automatic language detection and redirect logic in `src/middleware.ts`
- **i18n**: Translation system in `src/i18n/` with form translations

### Data Structure
- **Artists**: Ye and Playboi Carti with dedicated pages
- **Categories**: art, recent, best-of, unreleased for each artist
- **Eras**: Time-based organization for unreleased tracks (60+ eras for each artist)
- **Data Storage**: JSON files in `public/data/artists/` with hierarchical structure

### Component Architecture
- **Layout Components**: BaseLayout, Header, Footer, Sidebar
- **Category Views**: AlbumGridDisplay, ArtworkDisplay, TrackListDisplay
- **Interactive**: AudioPlayer, DownloadModal, ArtworkModal
- **Content Submission**: CollapsibleSubmissionForm, SimpleSubmissionForm

### Audio System
- Global audio player with playlist support
- Track download functionality with security measures
- Client-side audio state management
- Cross-component audio control events

## Key Files and Directories

### Configuration
- `astro.config.mjs` - Astro configuration with i18n, compression, security headers
- `tailwind.config.mjs` - Custom Tailwind theme with CSS animations
- `tsconfig.json` - TypeScript strict configuration with path aliases
- `vercel.json` - Deployment config with security headers

### Data Processing Scripts
- `scripts/extract-albums.js` - Processes HTML data into JSON structure
- `scripts/generate-og-images.js` - Creates social media preview images
- `scripts/generate-sitemaps.js` - Creates XML sitemaps for SEO
- `scripts/parse_html_to_json.py` - Python script for data extraction

### Core Components
- `src/components/categoryViews/TrackListDisplay.astro` - Complex track listing with audio playback
- `src/components/AudioPlayer.astro` - Global audio player component
- `src/components/content-submission/` - User content submission forms
- `src/middleware.ts` - Language detection and security headers

### Content Management
- `public/data/artists/` - Structured JSON data for artists, albums, tracks
- `public/images/artists/` - Artist images and artwork
- Data organized by artist → category → era hierarchy

## Development Workflow

### Adding New Content
1. Update JSON files in `public/data/artists/[artist]/categories/`
2. Run `npm run extract-albums` to process new data
3. Use `npm run generate-seo` to update sitemaps and OG images
4. Test with `npm run dev` before building

### SEO Optimization
- Run `npm run generate-seo` after content updates
- Check sitemap generation for new pages
- Verify OG image generation for social sharing
- Use `npm run indexnow` to notify search engines

### Multilingual Content
- Add translations to `public/data/i18n/form-translations.json`
- Update language-specific routing in middleware
- Test RTL layout for Arabic content

### Build Process
The build automatically:
1. Extracts and processes album data
2. Generates SEO assets (sitemaps, OG images, RSS feeds)
3. Compresses and optimizes assets
4. Submits to IndexNow for search indexing

## Security & Performance

### Security Headers
- CSP, HSTS, XSS protection configured in middleware and Vercel
- Content submission forms have built-in security validation
- Download functionality includes rate limiting and validation

### Performance Features
- Static site generation with Astro
- Image optimization with Sharp
- Asset compression with astro-compress
- Lazy loading for images and components

### Caching Strategy
- Static assets: 1 year cache
- Pages: 1 hour cache
- API responses: No cache

## Content Submission System

The platform includes a sophisticated content submission system allowing users to add tracks, albums, and artwork:

- Form validation with security measures in `src/components/content-submission/utils/SecurityUtils.js`
- Dynamic data loading in `DynamicDataLoader.js`
- Multilingual form support with translation system
- Rate limiting and content moderation features

Run tests after making changes to ensure data integrity and security validation work correctly.