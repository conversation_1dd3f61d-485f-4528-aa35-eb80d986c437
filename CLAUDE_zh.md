# CLAUDE_中文.md

此文件为 Claude Code (claude.ai/code) 提供在此代码库中工作时的指导。

## 项目概述

TrackerHive 是一个多语言音乐艺术家追踪平台，使用 Astro 5.6 构建，专门优化用于追踪 Ye (Kanye West) 和 Playboi Carti。该网站提供曲目列表、艺术作品画廊和音频播放功能，并进行了 SEO 优化。

## 技术栈

- **框架**: Astro 5.6 (静态站点生成)
- **样式**: Tailwind CSS 3.4 与自定义主题
- **UI 组件**: React 19.1 组件与 Astro 集成
- **语言**: 严格配置的 TypeScript
- **部署**: Vercel 静态输出
- **包管理器**: npm

## 核心开发命令

```bash
# 开发
npm run dev                    # 在 localhost:4321 启动开发服务器
npm run build                  # 构建生产站点到 ./dist/
npm run preview                # 本地预览构建结果

# 数据管理
npm run extract-albums         # 从 HTML 源提取和组织专辑数据
npm run download-images        # 从外部源下载和优化图片

# SEO 和内容生成
npm run generate-og            # 为艺术家/类别生成 Open Graph 图片
npm run generate-sitemap       # 为所有页面生成 XML 站点地图
npm run generate-rss           # 为艺术家更新生成 RSS 源
npm run generate-seo           # 运行所有 SEO 生成命令

# 内容提交
npm run indexnow               # 向 IndexNow 提交 URL 进行搜索索引
npm run indexnow-integration   # 构建后与 IndexNow 集成

# 分析和维护
npm run analyze-404            # 查找并修复重定向中的 404 错误
npm run verify-redirects       # 验证 Vercel 重定向配置

# 构建过程
npm run prebuild               # 运行 extract-albums 和 generate-seo
npm run postbuild              # 运行 indexnow-integration
```

## 架构概述

### 多语言支持
- **语言**: 英语（默认）、阿拉伯语（RTL）、葡萄牙语
- **路由**: 非英语语言使用语言前缀 (`/ar/*`, `/pt/*`)
- **中间件**: 在 `src/middleware.ts` 中自动语言检测和重定向逻辑
- **i18n**: `src/i18n/` 中的翻译系统，包含表单翻译

### 数据结构
- **艺术家**: Ye 和 Playboi Carti 拥有专用页面
- **类别**: 每个艺术家的 art、recent、best-of、unreleased
- **时代**: 未发布曲目的时间组织（每个艺术家 60+ 个时代）
- **数据存储**: `public/data/artists/` 中的分层结构 JSON 文件

### 组件架构
- **布局组件**: BaseLayout、Header、Footer、Sidebar
- **类别视图**: AlbumGridDisplay、ArtworkDisplay、TrackListDisplay
- **交互式**: AudioPlayer、DownloadModal、ArtworkModal
- **内容提交**: CollapsibleSubmissionForm、SimpleSubmissionForm

### 音频系统
- 支持播放列表的全局音频播放器
- 具有安全措施的曲目下载功能
- 客户端音频状态管理
- 跨组件音频控制事件

## 关键文件和目录

### 配置
- `astro.config.mjs` - Astro 配置，包括 i18n、压缩、安全头
- `tailwind.config.mjs` - 带 CSS 动画的自定义 Tailwind 主题
- `tsconfig.json` - 带路径别名的 TypeScript 严格配置
- `vercel.json` - 带安全头的部署配置

### 数据处理脚本
- `scripts/extract-albums.js` - 将 HTML 数据处理为 JSON 结构
- `scripts/generate-og-images.js` - 创建社交媒体预览图片
- `scripts/generate-sitemaps.js` - 为 SEO 创建 XML 站点地图
- `scripts/parse_html_to_json.py` - 用于数据提取的 Python 脚本

### 核心组件
- `src/components/categoryViews/TrackListDisplay.astro` - 带音频播放的复杂曲目列表
- `src/components/AudioPlayer.astro` - 全局音频播放器组件
- `src/components/content-submission/` - 用户内容提交表单
- `src/middleware.ts` - 语言检测和安全头

### 内容管理
- `public/data/artists/` - 艺术家、专辑、曲目的结构化 JSON 数据
- `public/images/artists/` - 艺术家图片和作品
- 数据按艺术家 → 类别 → 时代层次结构组织

## 开发工作流

### 添加新内容
1. 更新 `public/data/artists/[artist]/categories/` 中的 JSON 文件
2. 运行 `npm run extract-albums` 处理新数据
3. 使用 `npm run generate-seo` 更新站点地图和 OG 图片
4. 构建前使用 `npm run dev` 测试

### SEO 优化
- 内容更新后运行 `npm run generate-seo`
- 检查新页面的站点地图生成
- 验证社交分享的 OG 图片生成
- 使用 `npm run indexnow` 通知搜索引擎

### 多语言内容
- 向 `public/data/i18n/form-translations.json` 添加翻译
- 在中间件中更新特定语言的路由
- 测试阿拉伯语内容的 RTL 布局

### 构建过程
构建自动执行：
1. 提取和处理专辑数据
2. 生成 SEO 资产（站点地图、OG 图片、RSS 源）
3. 压缩和优化资产
4. 提交到 IndexNow 进行搜索索引

## 安全与性能

### 安全头
- 在中间件和 Vercel 中配置 CSP、HSTS、XSS 保护
- 内容提交表单具有内置安全验证
- 下载功能包括速率限制和验证

### 性能特性
- 使用 Astro 的静态站点生成
- 使用 Sharp 的图片优化
- 使用 astro-compress 的资产压缩
- 图片和组件的懒加载

### 缓存策略
- 静态资产：1年缓存
- 页面：1小时缓存
- API 响应：无缓存

## 内容提交系统

平台包含一个复杂的内容提交系统，允许用户添加曲目、专辑和艺术作品：

- 在 `src/components/content-submission/utils/SecurityUtils.js` 中具有安全措施的表单验证
- `DynamicDataLoader.js` 中的动态数据加载
- 带翻译系统的多语言表单支持
- 速率限制和内容审核功能

进行更改后运行测试，以确保数据完整性和安全验证正常工作。